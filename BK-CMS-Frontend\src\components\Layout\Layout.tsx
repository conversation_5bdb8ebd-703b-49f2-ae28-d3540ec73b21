import React, { useEffect, useState } from "react";
import {
  Avatar,
  Dropdown,
  Layout,
  Menu,
  MenuProps,
  message,
  notification,
  theme,
} from "antd";
import {
  UserOutlined,
  ShopOutlined,
  ShoppingCartOutlined,
  MenuOutlined,
  LogoutOutlined,
  Pic<PERSON>ightOutlined,
  DownOutlined,
  UsergroupAddOutlined,
} from "@ant-design/icons";
import { useNavigate, Outlet, useLocation } from "react-router-dom";
import logo from "../../assets/logo1.png";
import { axiosInstance } from "../../apiCalls";
import Cookies from "js-cookie";
import "../..//assets/css/Layout/Layout.css";
import { Spinner } from "react-bootstrap";
import {
  ACCESS_TOKEN,
  FIRST_NAME,
  LAST_NAME,
  LOGIN_PATH,
  REFRESH_TOKEN,
  USERNAME,
} from "../../constants/Constant";

// Layout Components
const { Header, Content, Sider } = Layout;

// Sidebar Content
const sideNavBarContent = [
  {
    id: "1",
    label: "Users",
    link: "users",
    icon: <UsergroupAddOutlined />,
  },
  {
    id: "2",
    label: "Orders",
    link: "orders",
    icon: <ShoppingCartOutlined />,
  },
  {
    id: "3",
    label: "Stores",
    link: "stores",
    icon: <ShopOutlined />,
  },
  {
    id: "4",
    label: "Kiosk",
    link: "kiosk",
    icon: <ShopOutlined />,
  },
  {
    id: "5",
    label: "Release-Notes",
    link: "release-notes",
    icon: <ShopOutlined />,
  },
  {
    id: "6",
    label: "Features",
    link: "features",
    icon: <ShopOutlined />,
  },
  {
    id: "7",
    label: "Creative Files",
    link: "creative-files",
    icon: <PicRightOutlined />,
  },
];

// Generate unique keys for Menu items
const items2 = sideNavBarContent.map((item) => ({
  key: item.id, // Unique key for top-level items
  label: item.label,
  link: item.link,
  icon: item.icon,
}));

const LayoutDesign: React.FC = () => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);

  // Determine selected key based on the current URL
  // Determine selected key based on the current URL
  const selectedKey =
    sideNavBarContent.find((item) => {
      const linkPath = `/${item.link.toLowerCase().replace(/\s+/g, "-")}`;
      return location.pathname.startsWith(linkPath);
    })?.id || "";

  const [collapsed, setCollapsed] = useState(false);
  const [username, setUsername] = useState<string | null>(null);

  useEffect(() => {
    //const storedUsername = Cookies.get("username");
    const storedFirstName = Cookies.get("first_name");
    const storedLastName = Cookies.get("last_name");
    if (storedFirstName && storedLastName) {
      setUsername(storedFirstName + " " + storedLastName);
    }
  }, []);

  // Logout Function
  const handleLogout = async () => {
    try {
      const refreshToken = Cookies.get("refreshToken");

      if (!refreshToken) {
        console.warn("Refresh token not found, skipping logout API call.");
        return;
      }
      setLoading(true);
      const response = await axiosInstance.post(
        "/cms/accounts/logout/",
        { refresh: refreshToken },
        { headers: { "Content-Type": "application/json" } }
      );

      if (response.status === 200) {
        Cookies.remove(ACCESS_TOKEN);
        Cookies.remove(REFRESH_TOKEN);
        Cookies.remove(USERNAME);
        Cookies.remove(FIRST_NAME);
        Cookies.remove(LAST_NAME);
        notification.success({
          message: "Logout successful",
          description: response.data.message,
        });
        window.location.href = LOGIN_PATH;
      } else {
        message.error("Failed to logout. Please try again.");
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        message.error(error.message || "Failed to logout. Please try again.");
      } else {
        message.error("An unexpected error occurred. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  // Logout Dropdown Menu Items
  const menuItems: MenuProps["items"] = [
    {
      key: "logout",
      label: (
        <span className="logout-text">
          {loading ? <Spinner className="me-2" /> : null} Logout
        </span>
      ),
      icon: <LogoutOutlined className="logout-icon" />,
      onClick: handleLogout,
    },
  ];

  const textFormat = (text: string | null) => {
    const username = Cookies.get("username");
    if (!text || text.trim() === "" || typeof text === "undefined") {
      return username || "Admin"; // Default value if null
    }

    return text
      .split(" ") // Split by spaces
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize first letter of each word
      .join(" "); // Join back into a single string
  };

  return (
    <Layout className="layout">
      <Header className="layout-header">
        <MenuOutlined
          className="menu-icon"
          onClick={() => setCollapsed(!collapsed)}
        />
        <img src={logo} alt="Logo" className="logo" />
        {/* <Button className="logout-button" onClick={handleLogout}>
          {loading ? (
            <Spinner
              as="span"
              animation="border"
              size="sm"
              role="status"
              aria-hidden="true"
              className="me-2"
            />
          ) : null}
          <LogoutOutlined />
        </Button> */}
        <Dropdown
          className="logout-dropdown"
          menu={{ items: menuItems }}
          trigger={["click"]}
          onOpenChange={setVisible}
          open={visible}
        >
          <div className={`dropdown-trigger ${visible ? "active" : ""}`}>
            <Avatar className="user-avatar">
              <UserOutlined />
            </Avatar>

            <span className="username">{textFormat(username)}</span>

            <DownOutlined
              className={`dropdown-icon ${visible ? "rotate" : ""}`}
            />
          </div>
        </Dropdown>
      </Header>
      <Layout>
        <Sider
          className="layout-sider"
          collapsible
          collapsed={collapsed}
          onCollapse={setCollapsed}
          style={{ background: colorBgContainer, borderRadius: borderRadiusLG }}
          breakpoint="lg"
          collapsedWidth={60}
          trigger={null}
        >
          <Menu
            className="layout-menu"
            mode="inline"
            selectedKeys={[selectedKey]}
            items={items2}
            onClick={(menuInfo) => {
              const mainItem = sideNavBarContent.find(
                (item) => item.id === menuInfo.key
              );
              if (mainItem) {
                const menuLink = `/${mainItem.link
                  .toLowerCase()
                  .replace(/\s+/g, "-")}-list`;
                localStorage.setItem("selectedMenuKey", mainItem.id); // Store selected menu key
                navigate(menuLink);
              }
            }}
          />
        </Sider>
        <Content
          className={`layout-content ${
            collapsed ? "collapsed-content" : "expanded-content"
          }`}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default LayoutDesign;
