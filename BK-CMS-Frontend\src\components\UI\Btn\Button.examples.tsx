import React, { useState } from 'react';
import { SaveOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import Button from './Button';
import { useButtonLoading } from './useButtonPerformance';

/**
 * Example usage of the performance-optimized Button component
 */
export const ButtonExamples: React.FC = () => {
  const [count, setCount] = useState(0);
  const { loading, withLoading } = useButtonLoading();

  const handleAsyncOperation = async () => {
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 2000));
    setCount(prev => prev + 1);
  };

  const handleDebouncedClick = () => {
    console.log('Debounced click executed');
    setCount(prev => prev + 1);
  };

  return (
    <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <h2>Performance-Optimized Button Examples</h2>
      
      {/* Basic variants */}
      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
        <Button variant="primary">Primary Button</Button>
        <Button variant="secondary">Secondary Button</Button>
        <Button variant="save" icon={<SaveOutlined />}>Save</Button>
        <Button variant="cancel">Cancel</Button>
        <Button variant="danger" icon={<DeleteOutlined />}>Delete</Button>
      </div>

      {/* Loading states */}
      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
        <Button 
          variant="save" 
          loading={loading}
          onClick={() => withLoading(handleAsyncOperation)}
        >
          Async Operation
        </Button>
        <Button 
          variant="primary" 
          loading={true}
          loadingText="Processing..."
        >
          Custom Loading Text
        </Button>
      </div>

      {/* Debounced button */}
      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
        <Button 
          variant="primary"
          debounce={true}
          debounceDelay={500}
          onClick={handleDebouncedClick}
        >
          Debounced Button (500ms)
        </Button>
        <span>Click count: {count}</span>
      </div>

      {/* Different sizes */}
      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
        <Button size="small" variant="primary">Small</Button>
        <Button size="middle" variant="primary">Middle</Button>
        <Button size="large" variant="primary">Large</Button>
      </div>

      {/* Icon positions */}
      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
        <Button 
          variant="primary" 
          icon={<PlusOutlined />} 
          iconPosition="left"
        >
          Icon Left
        </Button>
        <Button 
          variant="primary" 
          icon={<PlusOutlined />} 
          iconPosition="right"
        >
          Icon Right
        </Button>
      </div>

      {/* Full width */}
      <Button variant="save" fullWidth>
        Full Width Button
      </Button>

      {/* Disabled states */}
      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
        <Button variant="primary" disabled>Disabled Primary</Button>
        <Button variant="save" disabled>Disabled Save</Button>
      </div>

      {/* Custom styling */}
      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
        <Button 
          variant="primary" 
          className="btn-login"
          style={{ borderRadius: '20px' }}
        >
          Login Style
        </Button>
        <Button 
          variant="primary" 
          className="btn-back-home"
        >
          Back Home Style
        </Button>
      </div>
    </div>
  );
};

/**
 * Example of button performance monitoring
 */
export const ButtonPerformanceExample: React.FC = () => {
  const [metrics, setMetrics] = useState<any[]>([]);

  const showMetrics = () => {
    const allMetrics = (window as any).ButtonPerformanceMetrics?.getAllMetrics() || [];
    setMetrics(allMetrics);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h3>Button Performance Monitoring</h3>
      
      <div style={{ marginBottom: '16px' }}>
        <Button 
          id="monitored-button-1"
          variant="primary"
          onClick={() => console.log('Button 1 clicked')}
        >
          Monitored Button 1
        </Button>
        
        <Button 
          id="monitored-button-2"
          variant="save"
          onClick={() => console.log('Button 2 clicked')}
          style={{ marginLeft: '8px' }}
        >
          Monitored Button 2
        </Button>
        
        <Button 
          variant="secondary"
          onClick={showMetrics}
          style={{ marginLeft: '8px' }}
        >
          Show Metrics
        </Button>
      </div>

      {metrics.length > 0 && (
        <div>
          <h4>Performance Metrics:</h4>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            {JSON.stringify(metrics, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default ButtonExamples;
