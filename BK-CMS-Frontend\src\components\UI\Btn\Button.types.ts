import { ButtonProps as AntdButtonProps } from 'antd';

// Define custom button variants
export type ButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'save' 
  | 'cancel' 
  | 'danger' 
  | 'ghost' 
  | 'link' 
  | 'text';

// Define button sizes
export type ButtonSize = 'small' | 'middle' | 'large';

// Extended button props interface
export interface ButtonProps extends Omit<AntdButtonProps, 'type' | 'size'> {
  /** Button variant - determines styling */
  variant?: ButtonVariant;
  /** Button size */
  size?: ButtonSize;
  /** Loading state with custom loading text */
  loading?: boolean;
  /** Custom loading text when loading is true */
  loadingText?: string;
  /** Whether button should take full width */
  fullWidth?: boolean;
  /** Custom icon to display */
  icon?: React.ReactNode;
  /** Position of the icon */
  iconPosition?: 'left' | 'right';
  /** Tooltip text */
  tooltip?: string;
  /** Whether to prevent multiple rapid clicks */
  debounce?: boolean;
  /** Debounce delay in milliseconds */
  debounceDelay?: number;
  /** Custom CSS classes */
  className?: string;
  /** Children content */
  children?: React.ReactNode;
  /** Click handler */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>;
}

// Button theme configuration
export interface ButtonTheme {
  primary: string;
  secondary: string;
  save: string;
  cancel: string;
  danger: string;
}

// Performance optimization options
export interface ButtonPerformanceOptions {
  /** Whether to use React.memo */
  memoize?: boolean;
  /** Whether to debounce clicks */
  debounce?: boolean;
  /** Debounce delay */
  debounceDelay?: number;
  /** Whether to prevent default on click */
  preventDefault?: boolean;
}
