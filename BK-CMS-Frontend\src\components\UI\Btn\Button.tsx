import React, { memo, useCallback, useMemo, forwardRef, useEffect, useRef } from 'react';
import { Button as AntdButton, ButtonProps as AntdButtonProps } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import {
  mapVariantToAntdType,
  generateButtonClasses,
  getAccessibilityProps,
  ButtonPerformanceMetrics
} from './Button.utils';

// Re-export types
export type { ButtonVariant, ButtonSize, ButtonProps } from './Button.types';
import type { ButtonProps } from './Button.types';

// Performance-optimized Button component
const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  variant = 'primary',
  size = 'middle',
  loading = false,
  loadingText,
  fullWidth = false,
  icon,
  iconPosition = 'left',
  tooltip,
  debounce = false,
  debounceDelay = 300,
  className,
  children,
  onClick,
  disabled,
  ...rest
}, ref) => {
  // Debounced click handler to prevent rapid clicks
  const debouncedClickRef = React.useRef<NodeJS.Timeout | null>(null);
  const isClickingRef = React.useRef(false);

  const handleClick = useCallback(async (event: React.MouseEvent<HTMLButtonElement>) => {
    if (loading || disabled) return;

    // Track click for performance metrics
    if (metricsRef.current) {
      metricsRef.current.recordClick();
    }

    if (debounce) {
      if (isClickingRef.current) return;

      isClickingRef.current = true;

      if (debouncedClickRef.current) {
        clearTimeout(debouncedClickRef.current);
      }

      debouncedClickRef.current = setTimeout(() => {
        isClickingRef.current = false;
      }, debounceDelay);
    }

    if (onClick) {
      try {
        await onClick(event);
      } catch (error) {
        console.error('Button click handler error:', error);
      }
    }
  }, [onClick, loading, disabled, debounce, debounceDelay]);

  // Cleanup debounce timer on unmount
  React.useEffect(() => {
    return () => {
      if (debouncedClickRef.current) {
        clearTimeout(debouncedClickRef.current);
      }
    };
  }, []);

  // Performance metrics tracking
  const metricsRef = useRef<ButtonPerformanceMetrics | null>(null);

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const buttonId = rest.id || `button-${Math.random().toString(36).substring(2, 11)}`;
      metricsRef.current = ButtonPerformanceMetrics.getInstance(buttonId);
      metricsRef.current.recordRender();
    }
  });

  // Memoized variant-to-antd-type mapping
  const antdType = useMemo(() => mapVariantToAntdType(variant), [variant]);

  // Memoized CSS classes using utility function
  const buttonClasses = useMemo(() =>
    generateButtonClasses(variant, fullWidth, className),
    [variant, fullWidth, className]
  );

  // Memoized accessibility props
  const accessibilityProps = useMemo(() =>
    getAccessibilityProps({ loading, disabled, variant, tooltip }),
    [loading, disabled, variant, tooltip]
  );

  // Memoized loading icon
  const loadingIcon = useMemo(() => {
    return loading ? <LoadingOutlined spin /> : null;
  }, [loading]);

  // Memoized button content
  const buttonContent = useMemo(() => {
    if (loading && loadingText) {
      return (
        <>
          {loadingIcon}
          <span style={{ marginLeft: loadingIcon ? 8 : 0 }}>
            {loadingText}
          </span>
        </>
      );
    }

    if (loading) {
      return (
        <>
          {loadingIcon}
          {children && (
            <span style={{ marginLeft: loadingIcon ? 8 : 0 }}>
              {children}
            </span>
          )}
        </>
      );
    }

    if (icon && iconPosition === 'left') {
      return (
        <>
          {icon}
          {children && (
            <span style={{ marginLeft: 8 }}>
              {children}
            </span>
          )}
        </>
      );
    }

    if (icon && iconPosition === 'right') {
      return (
        <>
          {children && (
            <span style={{ marginRight: 8 }}>
              {children}
            </span>
          )}
          {icon}
        </>
      );
    }

    return children;
  }, [loading, loadingText, loadingIcon, children, icon, iconPosition]);

  // Memoized button props
  const buttonProps = useMemo(() => ({
    ref,
    type: antdType as AntdButtonProps['type'],
    size,
    loading: loading && !loadingText, // Use Antd's loading only if no custom loading text
    disabled: disabled || loading,
    className: buttonClasses,
    onClick: handleClick,
    title: tooltip,
    ...accessibilityProps,
    ...rest,
  }), [
    ref,
    antdType,
    size,
    loading,
    loadingText,
    disabled,
    buttonClasses,
    handleClick,
    tooltip,
    accessibilityProps,
    rest,
  ]);

  return (
    <AntdButton {...buttonProps}>
      {buttonContent}
    </AntdButton>
  );
});

Button.displayName = 'Button';

export default memo(Button);
