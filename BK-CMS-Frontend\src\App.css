#root {
  /* max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
     */
  width: 100%;
  height: 100%;
  /* text-align: center; */
  /* background-color: #f5f5f5; */
  color: #333;
  font-family: "Poppins", serif !important;
}

.search-result:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

/* back button */
.backButton {
  margin-bottom: 2;
  display: flex;
  align-items: center;
  background-color: #ff8732;
  color: #fff;
}
.backButton:hover {
  margin-bottom: 2;
  display: flex;
  align-items: center;
  background-color: #ff8732 !important;
  color: #fff !important;
  filter: drop-shadow(0 0 2em #ff8732aa);
}
.error-page-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.btn-back-home {
  background-color: #ff8732;
  color: #fff;
  border-color: #ff8732;
}

.btn-back-home:hover {
  background-color: #ff8732 !important;
  color: #fff !important;
  filter: drop-shadow(0 0 2em #ff8732aa); 
  border-color: #ff8732 !important;
}

/* search input field */
.search-box {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  width: 100%;
}

.search-input {
  flex: none;
  width: clamp(200px, 30%, 360px);
  height: 36px;
  padding: 0 16px;
  border: 2px solid #000 !important;
  border-radius: 18px;
  font-size: 14px;
  box-sizing: border-box;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  flex: none;
  height: 38px;
  padding: 0 20px;
  border: none;
  border-radius: 18px;
  background-color: #ff8c00;
  color: white;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}
.clear-btn {
  flex: none;
  height: 38px;
  padding: 0 20px;
  border-radius: 18px;
  border: 1px solid #d9d9d9;
  background-color: #f5f5f5;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}
.clear-all-btn {
  flex: none;
  height: 38px;
  padding: 0 20px;
  border-radius: 18px;
  border: 1px solid #d9d9d9;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

/* after antd.css is loaded */
.search-icone svg {
  background-color: #fff !important;
  border-radius: 50% !important;
  border: 1px solid #d9d9d9 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

@media screen and (max-width: 768px) {
  :root {
    overflow-x: auto;
    color: #213547;
    background-color: #ffffff;
  }
}
