import React, { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  <PERSON><PERSON>,
  Button,
  Row,
  Col,
  Card,
  Input,
  message,
  Switch,
  Form,
  notification,
  Spin,
} from "antd";
import { axiosInstance } from "../../../apiCalls";
import { Cancel, Save } from "../../../constants/Constant";

const AddStoreConfig: React.FC = () => {
  const { storeId } = useParams<{ storeId: string }>();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const [form] = Form.useForm(); // Initialize form instance

  const handleSubmit = async (values: any) => {
    if (!storeId) {
      message.error("Store ID is required!");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const payload = {
        store_id: Number(storeId),
        store_config_data: {
          payment_config: {
            qr: values.qr,
            card: values.card,
            cash: values.cash,
            edc_qr: values.edc_qr,
          },
          tax_config: {
            cgst: Number(values.cgst),
            sgst: Number(values.sgst),
          },
        },
      };

      const response = await axiosInstance.post(
        `cms/stores/create-store-config/`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        message.success("Store Created Successfully!");
        navigate(`/stores/${storeId}/details`);
      } else {
        throw new Error(
          `Unexpected response status: ${response.status}. Please try again.`
        );
      }
    } catch (error: any) {
      let errorMessage = "An unexpected error occurred.";

      if (error.response) {
        const errorData = error.response.data;

        if (typeof errorData === "object" && !Array.isArray(errorData)) {
          errorMessage = Object.entries(errorData)
            .map(([field, messages]) => {
              if (
                Array.isArray(messages) &&
                messages.every((msg) => typeof msg === "string")
              ) {
                return `${field}: ${messages.join(", ")}`;
              }
              return `${field}: ${String(messages)}`;
            })
            .join("\n"); // Formatting error messages
        } else {
          errorMessage = errorData?.message || JSON.stringify(errorData);
        }
      } else if (error.request) {
        errorMessage = "No response from server. Please check your network.";
      } else {
        errorMessage = error.message;
      }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mt-3">
      {loading && <Spin tip="Loading..." />}
      {error && <Alert message={error} type="error" closable />}
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <h2>Add Store Configuration</h2>
      </div>
      <div className="pt-4 mt-4">
        <Row gutter={[16, 24]} justify="center">
          <Col xs={20} sm={10}>
            <Card
              title="Payment & Tax Configuration"
              className="custom-card-edit-config"
            >
              {/* <span className="card-title">Payment & Tax Configuration:</span> */}
              <div className="mt-3">
                <Form
                  form={form}
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 20 }}
                  onFinish={handleSubmit}
                  initialValues={{
                    qr: false,
                    card: false,
                    cash: false,
                    edc_qr: false,
                  }}
                >
                  <Form.Item
                    label="QR Payment"
                    name="qr"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Form.Item
                    label="Card Payment"
                    name="card"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Form.Item
                    label="Cash Payment"
                    name="cash"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Form.Item
                    label="EDC QR Payment"
                    name="edc_qr"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="CGST"
                    name="cgst"
                    rules={[
                      { required: true, message: "Please input the CGST!" },
                      {
                        pattern: /^\d+(\.\d{1,2})?$/,
                        message:
                          "Only numbers with up to 2 decimal places are allowed!",
                      },
                    ]}
                    style={{ marginTop: 20 }}
                  >
                    <Input type="number" style={{ width: "150px" }} />
                  </Form.Item>

                  <Form.Item
                    label="SGST"
                    name="sgst"
                    rules={[
                      { required: true, message: "Please input the SGST!" },
                      {
                        pattern: /^\d+(\.\d{1,2})?$/,
                        message:
                          "Only numbers with up to 2 decimal places are allowed!",
                      },
                    ]}
                  >
                    <Input type="number" style={{ width: "150px" }} />
                  </Form.Item>

                  <div className="d-flex justify-content-center align-items-center flex-wrap gap-2 w-100">
                    <Button
                      className="btn-save flex-grow-1"
                      type="primary"
                      htmlType="submit"
                    >
                      {Save}
                    </Button>
                    <Button
                      className="btn-cancel flex-grow-1"
                      onClick={() => navigate(-1)}
                    >
                      {Cancel}
                    </Button>
                  </div>
                </Form>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default AddStoreConfig;
