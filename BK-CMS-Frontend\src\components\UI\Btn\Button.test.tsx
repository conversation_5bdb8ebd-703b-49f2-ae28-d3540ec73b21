import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Button from './Button';
import { SaveOutlined } from '@ant-design/icons';

describe('Button Component', () => {
  it('renders with default props', () => {
    render(<Button>Click me</Button>);
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
  });

  it('applies correct variant classes', () => {
    const { rerender } = render(<Button variant="save">Save</Button>);
    let button = screen.getByRole('button');
    expect(button).toHaveClass('btn-save');

    rerender(<Button variant="cancel">Cancel</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('btn-cancel');
  });

  it('handles loading state correctly', () => {
    render(<Button loading={true}>Loading</Button>);
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument(); // Loading icon
  });

  it('displays custom loading text', () => {
    render(
      <Button loading={true} loadingText="Saving...">
        Save
      </Button>
    );
    expect(screen.getByText('Saving...')).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('prevents clicks when disabled', () => {
    const handleClick = jest.fn();
    render(
      <Button disabled onClick={handleClick}>
        Disabled
      </Button>
    );
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('debounces rapid clicks', async () => {
    const handleClick = jest.fn();
    render(
      <Button debounce={true} debounceDelay={100} onClick={handleClick}>
        Debounced
      </Button>
    );
    
    const button = screen.getByRole('button');
    
    // Click multiple times rapidly
    fireEvent.click(button);
    fireEvent.click(button);
    fireEvent.click(button);
    
    // Should only be called once due to debouncing
    expect(handleClick).toHaveBeenCalledTimes(1);
    
    // Wait for debounce delay
    await waitFor(() => {
      fireEvent.click(button);
      expect(handleClick).toHaveBeenCalledTimes(2);
    }, { timeout: 200 });
  });

  it('renders with icon in correct position', () => {
    const { rerender } = render(
      <Button icon={<SaveOutlined />} iconPosition="left">
        Save
      </Button>
    );
    
    let button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    
    rerender(
      <Button icon={<SaveOutlined />} iconPosition="right">
        Save
      </Button>
    );
    
    button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('applies full width class', () => {
    render(<Button fullWidth>Full Width</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('w-100');
  });

  it('handles async click handlers', async () => {
    const asyncHandler = jest.fn().mockResolvedValue(undefined);
    render(<Button onClick={asyncHandler}>Async</Button>);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(asyncHandler).toHaveBeenCalledTimes(1);
    });
  });

  it('handles async click handler errors', async () => {
    const consoleError = jest.spyOn(console, 'error').mockImplementation();
    const asyncHandler = jest.fn().mockRejectedValue(new Error('Test error'));
    
    render(<Button onClick={asyncHandler}>Error</Button>);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(consoleError).toHaveBeenCalledWith(
        'Button click handler error:',
        expect.any(Error)
      );
    });
    
    consoleError.mockRestore();
  });

  it('applies accessibility attributes', () => {
    render(
      <Button 
        tooltip="Save your changes"
        loading={true}
        variant="danger"
      >
        Save
      </Button>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Save your changes');
    expect(button).toHaveAttribute('aria-busy', 'true');
  });

  it('forwards ref correctly', () => {
    const ref = React.createRef<HTMLButtonElement>();
    render(<Button ref={ref}>Ref Button</Button>);
    
    expect(ref.current).toBeInstanceOf(HTMLButtonElement);
  });

  it('merges custom className with variant classes', () => {
    render(
      <Button variant="save" className="custom-class">
        Custom
      </Button>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('btn-save');
    expect(button).toHaveClass('custom-class');
  });
});
