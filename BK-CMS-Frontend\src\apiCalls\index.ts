import axios from "axios";
import Cookies from "js-cookie";
import {
  ACCESS_TOKEN,
  FIRST_NAME,
  LAST_NAME,
  REFRESH_TOKEN,
  USERNAME,
} from "../constants/Constant";

const baseURL = import.meta.env.VITE_API_URL;

const axiosInstance = axios.create({
  baseURL: baseURL, // Your API base URL
  timeout: 30000, // 30 seconds timeout
  headers: {
    "Content-Type": "application/json",
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    const token = Cookies.get(ACCESS_TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// axiosInstance.interceptors.response.use(
//   (response) => response,
//   (error) => {
//     if (axios.isCancel(error) || error.code === "ERR_CANCELED") {
//       console.warn("Request canceled", error.message);
//       return Promise.reject(error);
//     }

//     if (error.response) {
//       const { status, data } = error.response;
//       switch (status) {
//         case 400:
//           console.error("Bad Request", data);
//           break;
//         case 401:
//           window.location.href = "/error401";
//           break;
//         case 403:
//           notification.error({
//             message: "Forbidden",
//             description: "You don't have access to this resource.",
//           });
//           break;
//         case 404:
//           console.error("Not Found", data);
//           break;
//         case 500:
//           notification.error({
//             message: "Server Error",
//             description: "Something went wrong on the server.",
//           });
//           break;
//         default:
//           notification.error({
//             message: `Error ${status}`,
//             description: data.message || "An error occurred.",
//           });
//       }
//     } else {
//       console.error("Unhandled Error", error);
//     }

//     return Promise.reject(error);
//   }
// );
let isRefreshing = false;
let refreshSubscribers: ((token: string) => void)[] = [];

function onRefreshed(token: string) {
  refreshSubscribers.forEach((callback) => callback(token));
  refreshSubscribers = [];
}

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    const isAuthEndpoint =
      originalRequest.url.includes("/login") ||
      originalRequest.url.includes("/token/refresh");
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !isAuthEndpoint
    ) {
      originalRequest._retry = true;

      if (!isRefreshing) {
        isRefreshing = true;
        try {
          const { data } = await axios.post(
            `${baseURL}/api/token/refresh/`,
            { refresh: Cookies.get(REFRESH_TOKEN) },
            { headers: { "Content-Type": "application/json" } }
          );
          Cookies.set(ACCESS_TOKEN, data.access, {
            secure: true,
            sameSite: "Strict",
          });
          Cookies.set(REFRESH_TOKEN, data.refresh, {
            secure: true,
            sameSite: "Strict",
          });
          isRefreshing = false;
          onRefreshed(data.access);
          originalRequest.headers.Authorization = `Bearer ${data.access}`;
          return axiosInstance(originalRequest);
        } catch (e) {
          isRefreshing = false;
          // Optionally clear cookies and redirect to login
          Cookies.remove(ACCESS_TOKEN);
          Cookies.remove(REFRESH_TOKEN);
          Cookies.remove(FIRST_NAME);
          Cookies.remove(LAST_NAME);
          Cookies.remove(USERNAME);
          window.location.href = "/";
          return Promise.reject(e);
        }
      }

      return new Promise((resolve) => {
        refreshSubscribers.push((token: string) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          resolve(axiosInstance(originalRequest));
        });
      });
    }
    return Promise.reject(error);
  }
);

export { axiosInstance };
