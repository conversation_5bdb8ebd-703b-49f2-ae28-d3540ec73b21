//for login props
export interface LoginUserProps {
  username: string;
  password: string;
}

//User List

export interface User {
  key: string;
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  is_staff: boolean;
  is_active: boolean;
  date_joined: string;
  is_superuser: boolean;
  user_type: string;
  last_login: string;
  groups: any[];
  user_permissions: any[];
}

// Stores List
export interface Store {
  key: string;
  id: number;
  name: string;
  code: string;
  timezone: number;
  address: string;
  phone: string;
  postal_code: string;
  latitude: number;
  longitude: number;
  tax_percentage: number;
  can_accept_delivery_order: boolean;
  is_active: boolean;
  coverage_type: string;
  third_party_id: string;
  take_away_charge: number;
  is_cash_payment_available: boolean;
  is_card_payment_available: boolean;
  is_qr_code_payment_available: boolean;
  created_at: string;
  updated_at: string;
  business: number;
  store_cofig: number;
  store_config_data?: StoreConfigData;
  banner_cofig: number;
  menu_cofig: number;
  banner_config: any | null; // Use a specific type instead of `any` if you know the structure.
  menu_config: any | null; // Use a specific type instead of `any` if you know the structure.
}

export interface StoreConfigData {
  config: Config;
  tax_config: TaxConfig;
  payment_config: PaymentConfig;
}

export interface Config {
  banner: BannerItem[];
  launch_page: LaunchPageItem[];
  service_page: ServicePage;
}

export interface BannerItem {
  source: string;
  content_type: "image" | "video";
}

export interface LaunchPageItem {
  source: string;
  content_type: "image" | "video";
}

export interface ServicePage {
  logo: string;
  image_url: string;
  welcome_text: string;
  service_options: ServiceOption[];
  background_color: string;
}

export interface ServiceOption {
  text: string;
  value: string;
  image_url: string;
}

export interface TaxConfig {
  cgst: number;
  sgst: number;
}

export interface PaymentConfig {
  qr: boolean;
  card: boolean;
  cash: boolean;
  edc_qr : boolean;
}

// Order List
export interface Order {
  key: string;
  id: number;
  table_number: number | null;
  order_type: string;
  order_status: "completed" | string;
  notes: string;
  payment_method: "upi_qr" | string;
  payment_status: "paid" | "unpaid" | string;
  payment_id: string | null;
  donation_enabled: boolean;
  grand_total: number | null;
  taxable_value: number | null;
  discount_value: number;
  rounding: number;
  pos_order_number: string | null;
  queue_number: number | null;
  kiosk_terminal_id: number | null;
  created_at: string; // ISO 8601 format
  customer: number;
  store: number;
}

export interface OrderItem {
  objects: Order[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

//individual menu item
export interface MenuItem {
  id: number;
  name: string;
  is_active: boolean;
  created_at: string; // ISO date format string
  updated_at: string; // ISO date format string
}

export interface MenuResponse {
  objects: MenuItem[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

// for Kiosk
export interface Kiosk {
  id: number;
  code: string;
  token: string;
  type: string;
  is_active: boolean;
  store: number;
}
