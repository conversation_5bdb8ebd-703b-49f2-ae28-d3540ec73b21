import { useCallback, useRef, useEffect, useState } from 'react';

interface UseButtonPerformanceOptions {
  /** Whether to debounce clicks */
  debounce?: boolean;
  /** Debounce delay in milliseconds */
  debounceDelay?: number;
  /** Whether button is disabled */
  disabled?: boolean;
  /** Whether button is loading */
  loading?: boolean;
}

interface UseButtonPerformanceReturn {
  /** Optimized click handler */
  handleClick: (
    originalHandler?: (event: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>
  ) => (event: React.MouseEvent<HTMLButtonElement>) => Promise<void>;
  /** Whether button is currently processing a click */
  isProcessing: boolean;
}

/**
 * Custom hook for button performance optimizations
 * Provides debouncing, loading state management, and click prevention
 */
export const useButtonPerformance = ({
  debounce = false,
  debounceDelay = 300,
  disabled = false,
  loading = false,
}: UseButtonPerformanceOptions = {}): UseButtonPerformanceReturn => {
  const debouncedClickRef = useRef<NodeJS.Timeout | null>(null);
  const isClickingRef = useRef(false);
  const isProcessingRef = useRef(false);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debouncedClickRef.current) {
        clearTimeout(debouncedClickRef.current);
      }
    };
  }, []);

  const handleClick = useCallback(
    (originalHandler?: (event: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>) =>
      async (event: React.MouseEvent<HTMLButtonElement>) => {
        // Prevent click if button is disabled or loading
        if (loading || disabled || isProcessingRef.current) {
          event.preventDefault();
          return;
        }

        // Debounce logic
        if (debounce) {
          if (isClickingRef.current) {
            event.preventDefault();
            return;
          }

          isClickingRef.current = true;

          if (debouncedClickRef.current) {
            clearTimeout(debouncedClickRef.current);
          }

          debouncedClickRef.current = setTimeout(() => {
            isClickingRef.current = false;
          }, debounceDelay);
        }

        // Execute original handler if provided
        if (originalHandler) {
          try {
            isProcessingRef.current = true;
            await originalHandler(event);
          } catch (error) {
            console.error('Button click handler error:', error);
          } finally {
            isProcessingRef.current = false;
          }
        }
      },
    [debounce, debounceDelay, disabled, loading]
  );

  return {
    handleClick,
    isProcessing: isProcessingRef.current,
  };
};

/**
 * Hook for managing button loading states
 */
export const useButtonLoading = (initialLoading = false) => {
  const [loading, setLoading] = useState(initialLoading);
  const loadingRef = useRef(loading);

  // Keep ref in sync with state
  useEffect(() => {
    loadingRef.current = loading;
  }, [loading]);

  const startLoading = useCallback(() => {
    setLoading(true);
  }, []);

  const stopLoading = useCallback(() => {
    setLoading(false);
  }, []);

  const withLoading = useCallback(
    async <T extends any[], R>(
      asyncFn: (...args: T) => Promise<R>,
      ...args: T
    ): Promise<R> => {
      if (loadingRef.current) {
        throw new Error('Operation already in progress');
      }

      try {
        startLoading();
        return await asyncFn(...args);
      } finally {
        stopLoading();
      }
    },
    [startLoading, stopLoading]
  );

  return {
    loading,
    startLoading,
    stopLoading,
    withLoading,
    isLoading: loadingRef.current,
  };
};

/**
 * Hook for button analytics and tracking
 */
export const useButtonAnalytics = (buttonId?: string) => {
  const clickCountRef = useRef(0);
  const lastClickTimeRef = useRef<number | null>(null);

  const trackClick = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      const now = Date.now();
      clickCountRef.current += 1;
      lastClickTimeRef.current = now;

      // You can integrate with analytics services here
      if (buttonId) {
        console.debug(`Button ${buttonId} clicked`, {
          clickCount: clickCountRef.current,
          timestamp: now,
          target: event.currentTarget,
        });
      }
    },
    [buttonId]
  );

  const getAnalytics = useCallback(() => ({
    clickCount: clickCountRef.current,
    lastClickTime: lastClickTimeRef.current,
  }), []);

  return {
    trackClick,
    getAnalytics,
  };
};
