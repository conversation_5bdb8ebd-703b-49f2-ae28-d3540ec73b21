body {
  font-family: "Poppins", serif !important;
}
.order-details-container {
  padding: 24px;
  background: #fff;
  min-height: 100vh;
}

/* .order-details-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 24px;
  } */

/* .heading-orders {
  border-bottom: 1px solid #f1f1f1;
  height: 50px;
  border-radius: 8px 8px 0 0; 
  transition: background-color 0.3s ease, color 0.3s ease;
  padding: 4px 16px; 
  background-color: #fff; 
} */

.store-name-highlight {
  color: #ff8732;
  font-weight: bold;
}
.order-detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
.button-back {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-top: 30px;
}

.button-back :hover {
  background-color: #ff8732 !important;
  color: #fff !important;
}

/* .button-back svg { */
/* li.ant-menu-item span {
  color: white !important;
}
li.ant-menu-item span svg {
  color: white !important;
}
svg path {
  color: white !important;
} */
.ant-card.ant-card-bordered.css-dev-only-do-not-override-1kf000u {
  border: none;
  padding: 0px !important;
}
/* .ant-card-body {
  padding: 0px !important;
} */

/* Item Details  table*/
/* General styles */
.items-container {
  font-family: "Poppins", serif;

  padding-right: 20px;
}

.items-title {
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: bold;
  color: #ff9900;
}

/* Items Table styles */
.items-table {
  width: 100%;
  border-collapse: collapse;
  overflow-x: auto;
  margin-bottom: 0px;
  font-size: 14px;
  margin-right: 20px;
  border: none !important;
}

.items-table-header th {
  padding: 10px;
  font-weight: bold;
  background-color: #f8f9fa;
  text-align: right;
}

.items-table-row td {
  padding: 10px;
  border-bottom: 1px solid #ddd;
  text-align: right;
}

/* Column-Specific Styling */
.items-table th:first-child,
.items-table td:first-child,
.items-table th:nth-child(2),
.items-table td:nth-child(2),
.items-table th:nth-child(3),
.items-table td:nth-child(3) {
  padding-left: 4px;
  text-align: left;
}

.items-table th:first-child,
.items-table td:first-child {
  width: 500px;
}

/* Totals  table section styles */
.totals-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
  border: none !important;
  margin-left: 300px;
  margin-right: 19px;
}

.totals-table {
  width: 40%;
  border-collapse: collapse;
  overflow-x: auto;
  text-align: center;
  font-size: 14px;
  margin-right: -26px;
  border: none !important;
}

.totals-label {
  padding: 10px;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}
.totals-label:first-child {
  text-align: left;
}

.totals-value {
  padding: 10px;
  border-bottom: 1px solid #ddd;
  text-align: right;
  padding-right: 29px;
}

.totals-grand-label {
  padding: 10px;
  font-weight: bold;
}
.totals-grand-label:first-child {
  text-align: left;
}

.totals-grand-value {
  padding: 10px;
  font-weight: bold;
  font-size: 18px;
  /* border-top: 2px solid #000; */
  padding-left: 30px;
}
.table th {
  color: #999999;
  min-width: 50px;
}
