# Performance-Optimized Button Component

A highly optimized, reusable button component built on top of Ant Design with enhanced performance features, accessibility, and developer experience.

## Features

### 🚀 Performance Optimizations
- **React.memo** for preventing unnecessary re-renders
- **useMemo** for expensive computations
- **useCallback** for stable function references
- **Debouncing** to prevent rapid clicks
- **Performance metrics** tracking in development mode
- **Lazy loading** of heavy operations

### ♿ Accessibility
- ARIA attributes for screen readers
- Keyboard navigation support
- Focus management
- Loading state announcements
- Proper semantic roles

### 🎨 Styling & Variants
- Multiple built-in variants (primary, save, cancel, danger, etc.)
- Custom CSS class integration
- Full width support
- Icon positioning (left/right)
- Size variants (small, middle, large)

### 🔧 Developer Experience
- TypeScript support with comprehensive types
- Performance monitoring in development
- Detailed prop validation
- Extensive documentation
- Example usage patterns

## Installation

```bash
# The component is already part of the UI components
import Button from '@/components/UI/Btn';
```

## Basic Usage

```tsx
import React from 'react';
import Button from '@/components/UI/Btn';
import { SaveOutlined } from '@ant-design/icons';

const MyComponent = () => {
  const handleSave = async () => {
    // Your save logic here
    await saveData();
  };

  return (
    <div>
      {/* Basic button */}
      <Button variant="primary" onClick={handleSave}>
        Save Changes
      </Button>

      {/* Button with icon */}
      <Button 
        variant="save" 
        icon={<SaveOutlined />}
        loading={isLoading}
        onClick={handleSave}
      >
        Save
      </Button>

      {/* Debounced button */}
      <Button 
        variant="primary"
        debounce={true}
        debounceDelay={500}
        onClick={handleRapidClicks}
      >
        Debounced Action
      </Button>
    </div>
  );
};
```

## Props API

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `ButtonVariant` | `'primary'` | Button style variant |
| `size` | `ButtonSize` | `'middle'` | Button size |
| `loading` | `boolean` | `false` | Loading state |
| `loadingText` | `string` | - | Custom loading text |
| `fullWidth` | `boolean` | `false` | Full width button |
| `icon` | `ReactNode` | - | Icon element |
| `iconPosition` | `'left' \| 'right'` | `'left'` | Icon position |
| `debounce` | `boolean` | `false` | Enable click debouncing |
| `debounceDelay` | `number` | `300` | Debounce delay in ms |
| `tooltip` | `string` | - | Tooltip text |
| `onClick` | `function` | - | Click handler |

### Button Variants

- `primary` - Primary action button
- `secondary` - Secondary action button  
- `save` - Save action with custom styling
- `cancel` - Cancel action with custom styling
- `danger` - Destructive action button
- `ghost` - Ghost/outline button
- `link` - Link-style button
- `text` - Text-only button

## Performance Features

### Debouncing
Prevent rapid successive clicks that could cause performance issues:

```tsx
<Button 
  debounce={true}
  debounceDelay={500}
  onClick={handleExpensiveOperation}
>
  Expensive Operation
</Button>
```

### Loading States
Optimized loading states with custom text:

```tsx
<Button 
  loading={isLoading}
  loadingText="Saving..."
  onClick={handleSave}
>
  Save Data
</Button>
```

### Performance Monitoring
In development mode, the component automatically tracks:
- Click count
- Render count
- Performance metrics
- Last click timestamp

Access metrics via browser console:
```javascript
// View all button metrics
console.log(ButtonPerformanceMetrics.getAllMetrics());
```

## Advanced Usage

### Custom Hook Integration
Use the provided performance hooks:

```tsx
import { useButtonLoading } from '@/components/UI/Btn/useButtonPerformance';

const MyComponent = () => {
  const { loading, withLoading } = useButtonLoading();

  const handleAsyncAction = async () => {
    await someAsyncOperation();
  };

  return (
    <Button 
      loading={loading}
      onClick={() => withLoading(handleAsyncAction)}
    >
      Async Action
    </Button>
  );
};
```

### Performance Optimization Tips

1. **Use debouncing** for buttons that trigger expensive operations
2. **Memoize click handlers** when passing complex functions
3. **Use loading states** to prevent multiple submissions
4. **Leverage variants** instead of custom styling when possible
5. **Monitor performance** in development mode

## Accessibility

The component includes comprehensive accessibility features:

- ARIA labels and descriptions
- Loading state announcements
- Keyboard navigation
- Focus management
- Screen reader support

## Migration from Ant Design Button

Replace existing Ant Design buttons:

```tsx
// Before
<Button type="primary" loading={loading}>
  Save
</Button>

// After  
<Button variant="primary" loading={loading}>
  Save
</Button>
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

When contributing to this component:

1. Maintain performance optimizations
2. Add comprehensive tests
3. Update documentation
4. Follow accessibility guidelines
5. Test across different browsers
