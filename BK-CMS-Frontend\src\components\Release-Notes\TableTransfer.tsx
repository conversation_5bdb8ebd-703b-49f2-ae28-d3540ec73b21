import { Table,  Transfer } from "antd";
import type { TableColumnsType, TableProps, TransferProps } from "antd";
import type { GetProp } from "antd";

type TransferItem = GetProp<TransferProps, 'dataSource'>[number];
type TableRowSelection<T extends object> = TableProps<T>['rowSelection'];

interface TableTransferProps extends TransferProps<TransferItem> {
  leftColumns: TableColumnsType<TransferItem>;
  rightColumns: TableColumnsType<TransferItem>;
}

const TableTransfer: React.FC<TableTransferProps> = ({ leftColumns, rightColumns, ...restProps }) => (
  <Transfer {...restProps} showSelectAll={false}>
    {({
      direction,
      filteredItems,
      onItemSelect,
      onItemSelectAll,
      selectedKeys,
      disabled,
    }) => {
      const columns = direction === 'left' ? leftColumns : rightColumns;
      const rowSelection: TableRowSelection<TransferItem> = {
        getCheckboxProps: () => ({ disabled }),
        onChange: (selectedRowKeys) => onItemSelectAll(selectedRowKeys, 'replace'),
        selectedRowKeys: selectedKeys,
      };

      return (
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={filteredItems}
          size="small"
          style={{ pointerEvents: disabled ? 'none' : undefined }}
          onRow={({ key, disabled: itemDisabled }) => ({
            onClick: () => {
              if (itemDisabled || disabled) return;
              onItemSelect(key, !selectedKeys.includes(key));
            },
          })}
        />
      );
    }}
  </Transfer>
);
export default TableTransfer;