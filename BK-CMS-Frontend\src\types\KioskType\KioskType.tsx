export interface KioskType {
  key: string;
  id: number;
  code: string;
  token: string;
  fcm_token: string;
  type: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  store: string;
  store_id: number;
  name: string;
  version: number;
  teamviewer_id: string;
  anydesk_id: string;
  edc_store_id: string;
  edc_securitytoken: string;
  edc_serial_number: string;
  printer_serial_number: string;
  store_location_type: string;
}
