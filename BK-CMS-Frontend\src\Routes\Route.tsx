import DashBoard from "../components/Dashboard/DashBoard";
import Error404Page from "../components/Error/Pages/Error404";
import AddFeatures from "../components/KioskFeatures/AddFeature/AddFeature";
import EditFeatures from "../components/KioskFeatures/FeatureDetails/EditFeature";
import FeatureDetails from "../components/KioskFeatures/FeatureDetails/FeatureDetails";
import AddKioskFeature from "../components/KioskFeatures/MappedKioskFeatures/AddFeatureToKisks";
import AddCreativeFilePage from "../pages/CreativeFilesPage/AddCreativeFilePage/AddCreativeFilePage";
import CreativeFileDetailsPage from "../pages/CreativeFilesPage/CreativeFileDetailsPage/CreativeFileDetailsPage";
import CreativeFileListPage from "../pages/CreativeFilesPage/CreativeFileListPage/CreativeFileListPage";
import MapStoresToCreativeFilePage from "../pages/CreativeFilesPage/MapStoresToCreativeFilePage/MapStoresToCreativeFilePage";
import KioskListPage from "../pages/Kiosk-Master-List/KioskListPage";
import KioskFeaturesPage from "../pages/KioskFeaturesPage/KioskFeaturesPage/KioskFeaturesPage";
import MenuMasterListPage from "../pages/Menu-Master-list/Menu-MasterListPage";
import OrderDetailsPage from "../pages/OrderPage/OrderDetailsPage/OrderDetailsPage";
import OrdersListPage from "../pages/OrderPage/OrderListTable/OrdersListPage";
import AddReleaseNotePage from "../pages/Release-Notes-Page/AddReleaseNotePage/AddReleaseNotePage";
import ReleaseNotesPage from "../pages/Release-Notes-Page/ReleaseNotesPage";
import AddStoreConfigPage from "../pages/Store_Mapping_Pages/Store-ConfigPage/AddStoreConfigPage/AddStoreConfigPage";
import StoreCreativeFileDetailsPage from "../pages/Store_Mapping_Pages/Store-CreativeFilePage/Store-CreativeFileDeatilsPage/StoreCreativeFileDetailsPage";
import AddKioskPage from "../pages/Store_Mapping_Pages/Store-KioskPage/AddKioskPage/AddKioskPage";
import EditKioskPage from "../pages/Store_Mapping_Pages/Store-KioskPage/EditKioskPage/EditKioskPage";
import StoreKioskDetailsPage from "../pages/Store_Mapping_Pages/Store-KioskPage/Store-KioskDetailsPage/Store-KioskDetailsPage";
import AddStorePage from "../pages/StorePage/AddStorePage/AddStorePage";
import BulkStoreUploadPage from "../pages/StorePage/BulkStoreUploadPage/BulkStoreUploadPage";
import EditStorePage from "../pages/StorePage/EditStorePage/EditStorePage";
import StoreDetailsPage from "../pages/StorePage/StoreDetailsPage/StoreDetailsPage";
import StoresListPage from "../pages/StorePage/StoreListPage/StoresListPage";
import AddUserPage from "../pages/UserPage/AddUserPage/AddUserPage";
import UserListPage from "../pages/UserPage/UserListPage";

const routes = [
  {
    path: "dashboard",
    element: <DashBoard />,
  },
  {
    path: "users-list",
    element: <UserListPage />,
  },
  {
    path: "register-user",
    element: <AddUserPage />,
  },
  {
    path: "orders-list",
    element: <OrdersListPage />,
  },
  {
    path: "order-details/:id",
    element: <OrderDetailsPage />,
  },
  {
    path: "stores-list",
    element: <StoresListPage />,
  },
  {
    path: "create-store",
    element: <AddStorePage />,
  },
  {
    path: "edit-store/:storeId",
    element: <EditStorePage />,
  },
  {
    path: "stores/upload-csv",
    element: <BulkStoreUploadPage />,
  },
  {
    path: "stores/:storeId/add-store-config",
    element: <AddStoreConfigPage />,
  },
  {
    path: "stores/:storeId/details",
    element: <StoreDetailsPage />,
  },
  {
    path: "stores/:storeId/kiosk",
    element: <StoreDetailsPage />,
  },
  {
    path: "stores/:storeId/menu",
    element: <StoreDetailsPage />,
  },
  {
    path: "stores/:storeId/creative-file",
    element: <StoreDetailsPage />,
  },
  {
    path: "stores/:storeId/:storeConfig/config",
    element: <StoreDetailsPage />,
  },
  {
    path: "menus-list",
    element: <MenuMasterListPage />,
  },
  {
    path: "stores/:storeId/add-kiosk",
    element: <AddKioskPage />,
  },
  {
    path: "stores/:storeId/kiosks/:kioskId/update",
    element: <EditKioskPage />,
  },
  {
    path: "store-kiosks-details/:storeId/:kioskId",
    element: <StoreKioskDetailsPage />,
  },
  {
    path: "store/features/:storeId/:kioskId/kiosks",
    element: <StoreKioskDetailsPage />,
  },
  {
    path: "kiosk-list",
    element: <KioskListPage />,
  },
  // {
  //   path: "banners-list",
  //   element: <MasterBannerListPage />,
  // },
  // {
  //   path: "banner-details/:id",
  //   element: <MasterBannerDetailsPage />,
  // },
  // {
  //   path: "add-new-Banner",
  //   element: <AddBannerPage />,
  // },
  // {
  //   path: "map-stores-to-banner/:bannerId",
  //   element: <MapStoresToBannerPage />,
  // },
  {
    path: "creative-files-list",
    element: <CreativeFileListPage />,
  },
  {
    path: "add-creative-files",
    element: <AddCreativeFilePage />,
  },
  {
    path: "creative-file-details/:id",
    element: <CreativeFileDetailsPage />,
  },
  {
    path: "creative-file-details/:id/stores",
    element: <CreativeFileDetailsPage />,
  },
  {
    path: "map-stores-to-creative-file/:id",
    element: <MapStoresToCreativeFilePage />,
  },
  {
    path: "store/:storeId/creative-file-details/:id",
    element: <StoreCreativeFileDetailsPage />,
  },
  {
    path: "features-list",
    element: <KioskFeaturesPage />,
  },
  {
    path: "add-kiosk-features",
    element: <AddFeatures />,
  },
  {
    path: "feature-details/:id",
    element: <FeatureDetails />,
  },
  {
    path: "feature-details/:id/edit",
    element: <EditFeatures />,
  },
  {
    path: "feature-details/:id/kiosks",
    element: <FeatureDetails />,
  },
  {
    path: "add-features-to-kiosk/:id",
    element: <AddKioskFeature />,
  },
  // Release-notes
  {
    path: "release-notes-list",
    element: <ReleaseNotesPage />,
  },
  {
    path: "add-new-release-note",
    element: <AddReleaseNotePage />,
  },

  {
    path: "*",
    element: <Error404Page />,
  },
];

export default routes;
