import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  <PERSON><PERSON>,
  Button,
  Switch,
  Form,
  notification,
  Input,
  Row,
  Col,
  Card,
} from "antd";
import { Store } from "../../../types";
import { axiosInstance } from "../../../apiCalls";
import { Cancel, Save } from "../../../constants/Constant";
import { Spinner } from "react-bootstrap";

const EditStoreConfig = ({
  storeId,
  store_cofig,
}: {
  storeId: string;
  store_cofig: string;
}) => {
  const { id } = useParams<{ id: string }>();
  const [stores, setStores] = useState<Store | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [form] = Form.useForm();

  const navigate = useNavigate();

  useEffect(() => {
    const fetchStoreDetails = async () => {
      try {
        setError(null);
        setLoading(true);
        const response = await axiosInstance.get(
          `cms/stores/store-details/${storeId}/`
        );

        if (response.status === 200) {
          const data = response.data;
          setStores(data);

          // Setting form fields correctly
          form.setFieldsValue({
            qr: data.store_config_data?.payment_config?.qr || false,
            card: data.store_config_data?.payment_config?.card || false,
            cash: data.store_config_data?.payment_config?.cash || false,
            edc_qr : data.store_config_data?.payment_config?.edc_qr || false,
            cgst: data.store_config_data?.tax_config?.cgst,
            sgst: data.store_config_data?.tax_config?.sgst,
          });
        } else {
          setError("Failed to load store details.");
        }
      } catch (error) {
        console.error("Error fetching store details:", error);
        setError("Failed to load store details.");
      } finally {
        setLoading(false);
        setIsLoaded(true);
      }
    };

    fetchStoreDetails();
  }, [id, storeId, form]);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      if (!stores || !stores.store_cofig) {
        throw new Error("Store configuration is missing.");
      }
      const payload = {
        store_id: Number(storeId),
        store_cofig: Number(store_cofig),
        data: {
          payment_config: {
            qr: values.qr,
            card: values.card,
            cash: values.cash,
            edc_qr : values.edc_qr,
          },
          tax_config: {
            cgst: Number(values.cgst),
            sgst: Number(values.sgst),
          },
        },
      };

      const response = await axiosInstance.patch(
        `cms/stores/store-config-update/${store_cofig}/`,
        payload
      );

      if (response.status === 200) {
        notification.success({
          message: "Store config updated successfully",
        });
      } else {
        notification.error({
          message: "Failed to update store config",
        });
      }
    } catch (error: any) {
      let errorMessage = "An unexpected error occurred.";

      if (error.response) {
        const errorData = error.response.data;

        if (typeof errorData === "object" && !Array.isArray(errorData)) {
          errorMessage = Object.entries(errorData)
            .map(([field, messages]) => {
              if (
                Array.isArray(messages) &&
                messages.every((msg) => typeof msg === "string")
              ) {
                return `${field}: ${messages.join(", ")}`;
              }
              return `${field}: ${String(messages)}`;
            })
            .join("\n"); // Formatting error messages
        } else {
          errorMessage = errorData?.message || JSON.stringify(errorData);
        }
      } else if (error.request) {
        errorMessage = "No response from server. Please check your network.";
      } else {
        errorMessage = error.message;
      }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading)
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <Spinner />
      </div>
    );
  if (error) return <Alert message={error} type="error" />;
  if (isLoaded && !stores)
    return <Alert message="Store not found." type="warning" />;

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Store Config</div>
      </div>

      <div className="pt-4 mt-2">
        <Row gutter={[16, 24]} justify="center">
          <Col xs={20} sm={10}>
            <Card
              title="Payment & Tax Configuration"
              className="custom-card-edit-config"
            >
              {/* <span className="card-title">Payment & Tax Configuration:</span> */}
              <div className="mt-3">
                <Form
                  form={form}
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 20 }}
                  onFinish={handleSubmit}
                >
                  <Form.Item
                    label="QR Payment"
                    name="qr"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Form.Item
                    label="Card Payment"
                    name="card"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Form.Item
                    label="Cash Payment"
                    name="cash"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="EDC QR Payment"
                    name="edc_qr"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="CGST"
                    name="cgst"
                    rules={[
                      { required: true, message: "Please input the CGST!" },
                      {
                        pattern: /^\d+(\.\d{1,2})?$/,
                        message:
                          "Only numbers with up to 2 decimal places are allowed!",
                      },
                    ]}
                    className="mt-3"
                  >
                    <Input type="number" className="input-width" />
                  </Form.Item>

                  <Form.Item
                    label="SGST"
                    name="sgst"
                    rules={[
                      { required: true, message: "Please input the SGST!" },
                      {
                        pattern: /^\d+(\.\d{1,2})?$/,
                        message:
                          "Only numbers with up to 2 decimal places are allowed!",
                      },
                    ]}
                  >
                    <Input type="number" className="input-width" />
                  </Form.Item>

                  {/* Save & Cancel Buttons in the Same Row */}

                  <div className="d-flex justify-content-center align-items-center flex-wrap gap-2">
                    <Button
                      className="btn-save"
                      type="primary"
                      htmlType="submit"
                    >
                      {Save}
                    </Button>
                    <Button className="btn-cancel" onClick={() => navigate(-1)}>
                      {Cancel}
                    </Button>
                  </div>
                </Form>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default EditStoreConfig;
