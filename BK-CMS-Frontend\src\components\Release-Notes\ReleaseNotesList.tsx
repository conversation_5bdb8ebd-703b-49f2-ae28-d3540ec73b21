import React, { useState, useEffect, useMemo, useRef } from "react";
import { Button, Input, InputRef } from "antd";
import { Link, useNavigate } from "react-router-dom";
import { ColumnsType } from "antd/es/table";
import { SearchOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { useTableFilters } from "../../hooks/useTableFilter";
import { ReleaseNoteType, ReleaseNoteTypeResponse } from "./releaseNoteType";
import { axiosInstance } from "../../apiCalls";
import { handleApiError } from "../../utils/ApiErrorHandler";
import ErrorFallback from "../Error/ErrorPage";
import CommonPagination from "../shared/Pagination/commonPagination";
import DataTable from "../shared/DataTable/commonDataTable";
import FilterButtons from "../shared/FilterButton/FilterButton";

const ReleaseNotesList: React.FC = () => {
  const {
    currentPage,
    pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    clearAllFilters,
  } = useTableFilters();
  const [data, setData] = useState<ReleaseNoteType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [searchValue, setSearchValue] = useState<string>("");
  const [isFocused, setIsFocused] = useState<boolean>(false);

  const navigate = useNavigate();

  const codeInputRef = useRef<InputRef>(null);

  // const abortControllerRef = useRef<AbortController | null>(null);
  const memoizedFilters = useMemo(() => filters, [filters]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchCreatives = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get<ReleaseNoteTypeResponse>(
          "/cms/stores/release-note/",
          {
            params: {
              page: currentPage,
              page_size: pageSize,
              ...memoizedFilters,
            },
            signal: controller.signal,
          }
        );

        if (response.status === 200) {
          setData(response.data?.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: any) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    fetchCreatives();

    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };

  const clearAllFiltersHandler = () => {
    clearAllFilters();
    setSearchValue("");
  };

  const handleFocus = () => {
    if (!isFocused) {
      setIsFocused(true);
      codeInputRef.current?.focus();
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const formatText = (text: string) => {
    if (!text) return "-";

    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const columns: ColumnsType<ReleaseNoteType> = useMemo(
    () => [
      {
        title: "ID",
        dataIndex: "id",
        key: "id",
        width: "10%",
        render: (code: string, record: ReleaseNoteType) => (
          <Link
            className="common-link text-decoration-none"
            to={`/releasing-note-details/${record.id}`}
          >
            {code}
          </Link>
        ),
      },
      {
        title: "Name",
        dataIndex: "name",
        key: "name",
      },
      {
        title: "Description",
        dataIndex: "description",
        key: "description",
      },
      {
        title: "Type",
        dataIndex: "notify_type",
        key: "notify_type",
        width: "15%",
        filteredValue: filters.type ? [filters.type] : null,
        // filterDropdown: (props) => (
        //   <FilterMenu
        //     {...props}
        //     filterKey="type"
        //     options={dynamicAttributes.creative_types.map((status) => ({
        //       label: formatText(status),
        //       value: status,
        //     }))}
        //     handleFilterChange={handleFilterChange}
        //   />
        // ),
        render: (value: string) => formatText(value),
      },
      {
        title: "Created At",
        dataIndex: "created_at",
        key: "created_at",
        render: (created: string) =>
          dayjs(created).format("DD MMM YYYY, hh:mm A"),
      },
    ],
    []
  );

  if (!loading && error) {
    return (
      <>
        <ErrorFallback error={error} onRetry={() => window.location.reload()} />
      </>
    );
  }

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Kiosk Release Notes</div>
        <Button
          type="primary"
          className="add-store"
          onClick={() => navigate(`/add-new-release-note`)}
        >
          Add New Release Note
        </Button>
      </div>
      <div className="d-flex justify-content-between align-items-center mb-3 mt-4 flex-wrap">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <Input.Search
              ref={codeInputRef}
              placeholder="Search by Name"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={(value) => {
                if (value.trim()) {
                  handleSearchChange(value);
                }
              }}
              onFocus={handleFocus}
              onBlur={handleBlur} // Keep icon if text exists
              prefix={
                <SearchOutlined
                  style={{ visibility: isFocused ? "visible" : "hidden" }}
                />
              }
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearAllFilters={clearAllFiltersHandler}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatText}
              filters={filters}
            />
          </div>
        </div>
      </div>

      <div className="pt-2 mt-2">
        <DataTable<ReleaseNoteType>
          dataSource={data}
          columns={columns}
          rowKey={(record) => record.id}
          pagination={false}
          loading={loading}
          // scroll={{ x: 1000 }}
        />
      </div>
      <CommonPagination
        current={currentPage}
        pageSize={pageSize}
        total={totalCount}
        showSizeChanger
        onShowSizeChange={handlePageChange}
        onChange={handlePageChange}
      />
    </div>
  );
};

export default ReleaseNotesList;
