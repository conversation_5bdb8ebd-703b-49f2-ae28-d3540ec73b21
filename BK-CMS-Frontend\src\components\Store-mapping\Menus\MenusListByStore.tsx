import { useState, useEffect } from "react";
import { message, Modal } from "antd";
import { axiosInstance } from "../../../apiCalls";
// import { MenuByStoreProps } from "../../../types/MenusType/MenuListByStore";
import { MenuByStoreProps } from "../../../types/MenuTypeByStore/MenuByStoreProps";
import dayjs from "dayjs";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { handleApiError } from "../../../utils/ApiErrorHandler";
import ErrorFallback from "../../Error/ErrorPage";
import CommonPagination from "../../shared/Pagination/commonPagination";
import { useTableFilters } from "../../../hooks/useTableFilter";
import DataTable from "../../shared/DataTable/commonDataTable";
interface MenuType {
  id: number;
  code: string;
  display_name: string;
  is_active: boolean;
}
const StoreWiseMenuList = ({ storeId }: MenuByStoreProps) => {
  const { currentPage, pageSize, handlePageChange } = useTableFilters();
  const [products, setProducts] = useState<MenuType[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(
        `/cms/menu/store-menus/?store_id=${storeId}`,
        {
          params: {
            page: currentPage,
            page_size: pageSize,
          },
        }
      );
      if (response.status === 200) {
        setProducts(response.data.objects);
        setTotalCount(response.data.total_count);
        setError(null);
      } else {
        setError(`Unexpected response status: ${response.status}`);
      }
    } catch (err: unknown) {
      handleApiError(err, setError);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [currentPage, pageSize, storeId]);

  const handleStatusChange = async (id: number, isActive: boolean) => {
    try {
      setLoading(true);

      // Show confirmation before deactivating
      if (!isActive) {
        Modal.confirm({
          title: "Deactivate menu",
          content: "Are you sure you want to deactivate this menu?",
          okText: "Yes",
          cancelText: "No",
          className: "custom-modal", // Apply custom class
          okButtonProps: { className: "custom-modal-ok-button" }, // Apply button class
          cancelButtonProps: { className: "custom-modal-cancel-button" },
          onOk: async () => {
            await updateBannerStatus(id, isActive);
          },
        });
      } else {
        await updateBannerStatus(id, isActive);
      }
    } catch (err: any) {
      message.error("Error updating menu status.");
    } finally {
      setLoading(false);
    }
  };

  const updateBannerStatus = async (id: number, isActive: boolean) => {
    try {
      const response = await axiosInstance.put(
        `cms/menu/store-menu-details/${id}/`,
        {
          is_active: isActive,
        }
      );

      message.success(response.data.message);
      setProducts((prev) =>
        prev.map((menu) =>
          menu.id === id
            ? {
                ...menu,
                is_active: isActive,
                updated_at: response.data.updated_at,
              }
            : menu
        )
      );
    } catch (err: any) {
      message.error(
        err.response?.data?.message || "Error updating menu status."
      );
    }
  };

  const handleRetry = () => {
    setError(null);
    fetchProducts();
  };

  // const handleStatusChange = async (id: number, isActive: boolean) => {
  //   try {
  //     setLoading(true);

  //     // Check if another banner is already active
  //     const activeMenu = products.find((product) => product.is_active);

  //     if (!isActive) {
  //       // Show confirmation before deactivating
  //       Modal.confirm({
  //         title: "Deactivate Menu",
  //         content: "Are you sure you want to deactivate this banner?",
  //         okText: "Yes",
  //         cancelText: "No",
  //         className: "custom-modal", // Apply custom class
  //         okButtonProps: { className: "custom-modal-ok-button" }, // Apply button class
  //         cancelButtonProps: { className: "custom-modal-cancel-button" },
  //         onOk: async () => {
  //           await updateMenuStatus(id, isActive);
  //         },
  //       });
  //     } else if (activeMenu && activeMenu.id !== id) {
  //       // Prevent activating another menu if one is already active
  //       notification.error({
  //         message: "Error",
  //         description:
  //           "Another Menu is already active. Please deactivate it first.",
  //       });
  //     } else {
  //       await updateMenuStatus(id, isActive);
  //     }
  //   } catch (err) {
  //     message.error("Error updating Menu status.");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // const updateMenuStatus = async (id: number, isActive: boolean) => {
  //   try {
  //     await axiosInstance.put(`cms/menu/store-menu-details/${id}/`, {
  //       is_active: isActive,
  //     });

  //     message.success("Menu status updated.");
  //     setProducts((prev) =>
  //       prev.map((menu) =>
  //         menu.id === id ? { ...menu, is_active: isActive } : menu
  //       )
  //     );
  //   } catch (err) {
  //     message.error("Error updating Menu status.");
  //   }
  // };

  const columns = [
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
      width: "15%",
    },
    {
      title: "name",
      dataIndex: "name",
      key: "name",
      width: "15%",
    },
    {
      title: "Display Name",
      dataIndex: "display_name",
      key: "display_name",
      width: "20%",
    },
    {
      title: "Created Date",
      dataIndex: "created_at",
      key: "created_at",
      width: "17%",
      render: (updated: string) =>
        dayjs(updated).format("DD MMM YYYY, hh:mm A"),
    },
    {
      title: "Updated Date",
      dataIndex: "updated_at",
      key: "updated_at",
      width: "17%",
      render: (updated: string) =>
        dayjs(updated).format("DD MMM YYYY, hh:mm A"),
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "status",
      width: "10%",
      fixed: "right" as "right",
      render: (is_active: boolean, record: MenuType) => (
        <div className="d-flex">
          <div
            className={`switch-button ${is_active ? "checked" : ""}`}
            onClick={() => handleStatusChange(record.id, !is_active)}
          >
            <span className="switch-label">
              {" "}
              {is_active ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
  ];
  //   if (loading) {
  //     return <div>Loading...</div>;
  //   }
  if (!loading && error) {
    return <ErrorFallback error={error} onRetry={handleRetry} />;
  }

  return (
    <div className="mt-3">
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Menu List</div>
      </div>
      <div className="pt-4 mt-4">
        <DataTable<MenuType>
          bordered
          dataSource={products}
          columns={columns}
          rowKey="id"
          loading={loading}
          scroll={{ x: "max-content" }}
          pagination={false}
        />
        <CommonPagination
          current={currentPage}
          total={totalCount}
          pageSize={pageSize}
          showSizeChanger={true}
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
        />
      </div>
    </div>
  );
};
export default StoreWiseMenuList;
