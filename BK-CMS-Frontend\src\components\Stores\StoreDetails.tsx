import React, { useEffect, useState } from "react";
import {
  useParams,
  useLocation,
  useNavigate,
  useMatch,
} from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import { Tabs, Spin, Button, Modal, message, Alert } from "antd";
import KioskListByStore from "../Store-mapping/Kiosk/KioskListByStore";
import StoreWiseMenuList from "../Store-mapping/Menus/MenusListByStore";
import EditStoreConfig from "../Store-mapping/Store-Config/EditStoreConfig";
import { Store } from "../../types";
import {
  CheckOutlined,
  CloseOutlined,
  DoubleRightOutlined,
} from "@ant-design/icons";
import { Cancel, Edit, Retry, Store_Detail } from "../../constants/Constant";
import { handleApiError } from "../../utils/ApiErrorHandler";
import StoreCreativeFileList from "../Store-mapping/Strore-Banner/StoreCreativeFile/StoreCreativeFileList";
import "../../components/Stores/StoreDetails.css";

const StoreDetails: React.FC = () => {
  const [store, setStore] = useState<Store | undefined>(undefined);
  const [storeConfig, setStoreConfig] = useState<string | undefined>();
  const [storeName, setStoreName] = useState<string>("");
  const [isActive, setIsActive] = useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const { storeId, storeConfig: storeConfigParam } = useParams<{
    storeId: string;
    storeConfig?: string;
  }>();

  const location = useLocation();
  const navigate = useNavigate();

  const tabs = [
    { name: "Details", link: `/stores/${storeId}/details` },
    { name: "Kiosk", link: `/stores/${storeId}/kiosk` },
    { name: "Menu", link: `/stores/${storeId}/menu` },
    { name: "Creative File", link: `/stores/${storeId}/creative-file` },
    {
      name: "Config",
      link: `/stores/${storeId}/${
        storeConfig || storeConfigParam || ""
      }/config`,
    },
  ];

  const tabItems = tabs.map((tab) => ({
    key: tab.link,
    label: <span className="custom-tab">{tab.name}</span>,
    children: null,
  }));

  // Use useMatch to check the current route.
  const detailsMatch = useMatch(`/stores/${storeId}/details`);
  const kioskMatch = useMatch(`/stores/${storeId}/kiosk`);
  const menuMatch = useMatch(`/stores/${storeId}/menu`);
  const bannerMatch = useMatch(`/stores/${storeId}/creative-file`);
  const configMatch = useMatch(
    `/stores/${storeId}/${storeConfig || storeConfigParam || ""}/config`
  );

  const getStoreDetails = async (signal?: AbortSignal) => {
    try {
      const response = await axiosInstance.get(
        `/cms/stores/store-details/${storeId}/`,
        { signal }
      );
      if (response.status === 200) {
        const data = response.data;
        setStore(data);
        setStoreName(data.name);

        setStoreConfig(data.store_cofig?.toString() || "");

        setIsActive(data.is_active);
      } else {
        setError("Failed to fetch data");
      }
    } catch (err: unknown) {
      handleApiError(err, setError);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const controller = new AbortController();
    setLoading(true);
    getStoreDetails(controller.signal);
    return () => {
      controller.abort();
    };
  }, [storeId]);

  const navigateWithConfig = (path: string) => {
    if (!storeConfig && path.includes("config")) {
      setIsModalVisible(true);
      return;
    }
    navigate(path);
  };

  // Redirect function for adding store config.
  const handleAddConfigRedirect = () => {
    setIsModalVisible(false);
    navigate(`/stores/${storeId}/add-store-config/`);
  };

  // Handle store status change with confirmation.
  const handleStatusChange = (checked: boolean): void => {
    Modal.confirm({
      title: checked ? "Activate Store" : "Deactivate Store",
      content: checked
        ? "Are you sure you want to activate this store?"
        : "Are you sure you want to deactivate this store?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      async onOk() {
        setLoading(true);
        try {
          const response = await axiosInstance.patch(
            `/cms/stores/update-store/${storeId}/`,
            { is_active: checked }
          );
          if (response.status === 200) {
            message.success("Store status updated successfully.");
            setIsActive(checked);
            await getStoreDetails();
          } else {
            message.error("Failed to update store status.");
          }
        } catch (err: unknown) {
          handleApiError(err, setError);
          message.error("An error occurred while updating store status.");
        } finally {
          setLoading(false);
        }
      },
    });
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center h-100">
        <Spin size="large" />
      </div>
    );
  }

  if (!loading && error) {
    return (
      <div className="error-container">
        <Alert message="Error" description={error} type="error" showIcon />
        <div className="retry-button d-flex justify-content-center align-items-center mt-3">
          <Button type="primary" onClick={() => window.location.reload()}>
            {Retry}
          </Button>
        </div>
      </div>
    );
  }

  if (!store) {
    return <div>Store data not available</div>;
  }

  // Render store details in a card-like layout.
  const renderDetailsInCard = () => {
    const fields = [
      { label: "Store", key: "name" as keyof Store },
      { label: "Code", key: "code" as keyof Store },
      { label: "ATO ID", key: "ato_id" as keyof Store },
      { label: "Timezone", key: "timezone" as keyof Store },
      { label: "Address", key: "address" as keyof Store },
      { label: "Phone", key: "phone" as keyof Store },
      { label: "Postal Code", key: "postal_code" as keyof Store },
      // { label: "EDC Store ID", key: "edc_store_id" as keyof Store },
      { label: "EDC Merchant ID", key: "edc_merchant_id" as keyof Store },
      { label: "Third Party ID", key: "third_party_id" as keyof Store },
      { label: "Latitude", key: "latitude" as keyof Store },
      { label: "Longitude", key: "longitude" as keyof Store },
      { label: "Tax Percentage", key: "tax_percentage" as keyof Store },
      {
        label: "Can Accept Delivery Order",
        key: "can_accept_delivery_order" as keyof Store,
      },
      { label: "Is Active", key: "is_active" as keyof Store },
      // { label: "Created At", key: "created_at" as keyof Store },
      // { label: "Updated At", key: "updated_at" as keyof Store },
      { label: "Coverage Type", key: "coverage_type" as keyof Store },
      { label: "Take Away Charge", key: "take_away_charge" as keyof Store },
    ];

    // return fields.map((field) => {
    //   const value = store[field.key];
    //   const formattedValue =
    //     value === undefined || value === null || value === ""
    //       ? "-"
    //       : typeof value === "boolean"
    //       ? value
    //         ? "Yes"
    //         : "No"
    //       : typeof value === "number"
    //       ? value.toFixed(7)
    //       : value;
    return fields.map((field) => {
      const value = store[field.key];

      let formattedValue;
      if (value === undefined || value === null || value === "") {
        formattedValue = "-";
      } else if (typeof value === "boolean") {
        formattedValue = value ? "Yes" : "No";
      } else if (typeof value === "number") {
        if (field.key === "latitude" || field.key === "longitude") {
          formattedValue = String(value);
          //  formattedValue = value.toFixed(5);
        } else {
          formattedValue = value.toFixed(1);
        }
      } else {
        formattedValue = value;
      }
      return (
        <div key={field.key.toString()} className="order-details-value">
          <div className="order-details-label">{field.label}</div>
          <span className="order-details-value-colon">:</span>
          <span className="order-details-value-value">{formattedValue}</span>
        </div>
      );
    });
  };

  return (
    <div>
      <div className="w-full max-w-3xl mx-auto">
        {/* Tab Navigation */}
        <div className="tabs-navigation">
          <span className="store-name-text">
            {storeName} <DoubleRightOutlined />
          </span>
          <Tabs
            className="responsive-tabs"
            activeKey={location.pathname}
            onChange={(key) => navigateWithConfig(key)}
            tabBarStyle={{ borderBottom: "none" }}
            moreIcon={<DoubleRightOutlined />}
            items={tabItems}
          />
        </div>

        {/* Tab Content */}
        <div className="max-w-3xl w-full">
          {detailsMatch && (
            <div>
              <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
                <div>{Store_Detail}</div>
                <div className="d-flex">
                  <div
                    className={`switch-button ${isActive ? "checked" : ""}`}
                    onClick={() => handleStatusChange(!isActive)}
                  >
                    <span className="switch-label">
                      {isActive ? <CheckOutlined /> : <CloseOutlined />}
                    </span>
                    <div className="switch-handle"></div>
                  </div>
                  <Button
                    type="primary"
                    onClick={() => navigate(`/edit-store/${storeId}/`)}
                  >
                    {Edit}
                  </Button>
                </div>
              </div>
              <div className="order-details-card">{renderDetailsInCard()}</div>
            </div>
          )}

          {kioskMatch && <KioskListByStore storeId={storeId || ""} />}
          {menuMatch && <StoreWiseMenuList storeId={storeId || ""} />}
          {/* {bannerMatch && <StoreBanner storeId={storeId || ""} />} */}
          {bannerMatch && <StoreCreativeFileList storeId={storeId || ""} />}
          {configMatch && (
            <EditStoreConfig
              storeId={storeId || ""}
              store_cofig={storeConfig || storeConfigParam || ""}
            />
          )}
        </div>
      </div>

      {/* Modal for Missing Store Config */}
      <Modal
        title="Missing Store Configuration"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={[
          <Button
            className="custom-modal-cancel-button"
            key="cancel"
            onClick={() => setIsModalVisible(false)}
          >
            {Cancel}
          </Button>,
          <Button
            key="addConfig"
            type="primary"
            className="custom-modal-ok-button"
            onClick={handleAddConfigRedirect}
          >
            Add Store Config
          </Button>,
        ]}
      >
        <p>
          This store does not have a configuration. Would you like to add one?
        </p>
      </Modal>
    </div>
  );
};

export default StoreDetails;
