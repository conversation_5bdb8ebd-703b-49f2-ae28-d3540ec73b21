import { ButtonVariant, ButtonSize } from './Button.types';

/**
 * Maps custom button variants to Ant Design button types
 */
export const mapVariantToAntdType = (variant: ButtonVariant): string => {
  switch (variant) {
    case 'primary':
    case 'save':
      return 'primary';
    case 'secondary':
    case 'cancel':
      return 'default';
    case 'danger':
      return 'primary';
    case 'ghost':
      return 'ghost';
    case 'link':
      return 'link';
    case 'text':
      return 'text';
    default:
      return 'primary';
  }
};

/**
 * Generates CSS classes based on button variant and other props
 */
export const generateButtonClasses = (
  variant: ButtonVariant,
  fullWidth: boolean,
  customClassName?: string
): string => {
  const classes: string[] = [];

  // Variant-specific classes
  switch (variant) {
    case 'save':
      classes.push('btn-save');
      break;
    case 'cancel':
      classes.push('btn-cancel');
      break;
    case 'danger':
      classes.push('ant-btn-dangerous');
      break;
  }

  // Full width class
  if (fullWidth) {
    classes.push('w-100');
  }

  // Custom classes from className prop
  if (customClassName) {
    // Handle special cases for existing CSS classes
    if (customClassName.includes('btn-login')) {
      classes.push('btn-login');
    }
    if (customClassName.includes('btn-back-home')) {
      classes.push('btn-back-home');
    }
    if (customClassName.includes('add-store')) {
      classes.push('add-store-button');
    }
    
    // Add other custom classes
    const otherClasses = customClassName
      .split(' ')
      .filter(cls => !['btn-login', 'btn-back-home', 'add-store'].some(special => cls.includes(special)));
    classes.push(...otherClasses);
  }

  return classes.join(' ');
};

/**
 * Validates button props and provides warnings for potential issues
 */
export const validateButtonProps = (props: {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  disabled?: boolean;
  children?: React.ReactNode;
}): string[] => {
  const warnings: string[] = [];

  // Check for conflicting states
  if (props.loading && props.disabled) {
    warnings.push('Button has both loading and disabled states. Loading state will take precedence.');
  }

  // Check for missing content
  if (!props.children && !props.loading) {
    warnings.push('Button has no content. Consider adding children or an icon.');
  }

  // Check for accessibility concerns
  if (props.variant === 'link' && !props.children) {
    warnings.push('Link buttons should have descriptive text for accessibility.');
  }

  return warnings;
};

/**
 * Debounce utility function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Throttle utility function
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};

/**
 * Creates a memoized click handler to prevent unnecessary re-renders
 */
export const createMemoizedClickHandler = (
  handler: (event: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>,
  dependencies: any[]
) => {
  // This would typically use useCallback in the component
  // This is a utility for manual memoization if needed
  return handler;
};

/**
 * Button performance metrics
 */
export class ButtonPerformanceMetrics {
  private static instances = new Map<string, ButtonPerformanceMetrics>();
  private clickCount = 0;
  private renderCount = 0;
  private lastClickTime: number | null = null;
  private createdAt = Date.now();

  constructor(private buttonId: string) {}

  static getInstance(buttonId: string): ButtonPerformanceMetrics {
    if (!this.instances.has(buttonId)) {
      this.instances.set(buttonId, new ButtonPerformanceMetrics(buttonId));
    }
    return this.instances.get(buttonId)!;
  }

  recordClick(): void {
    this.clickCount++;
    this.lastClickTime = Date.now();
  }

  recordRender(): void {
    this.renderCount++;
  }

  getMetrics() {
    return {
      buttonId: this.buttonId,
      clickCount: this.clickCount,
      renderCount: this.renderCount,
      lastClickTime: this.lastClickTime,
      createdAt: this.createdAt,
      averageClicksPerRender: this.renderCount > 0 ? this.clickCount / this.renderCount : 0,
    };
  }

  reset(): void {
    this.clickCount = 0;
    this.renderCount = 0;
    this.lastClickTime = null;
    this.createdAt = Date.now();
  }

  static getAllMetrics() {
    return Array.from(this.instances.values()).map(instance => instance.getMetrics());
  }

  static clearAllMetrics(): void {
    this.instances.clear();
  }
}

/**
 * Accessibility helpers for buttons
 */
export const getAccessibilityProps = (props: {
  loading?: boolean;
  disabled?: boolean;
  variant?: ButtonVariant;
  tooltip?: string;
}) => {
  const accessibilityProps: Record<string, any> = {};

  // ARIA attributes
  if (props.loading) {
    accessibilityProps['aria-busy'] = true;
    accessibilityProps['aria-live'] = 'polite';
  }

  if (props.disabled) {
    accessibilityProps['aria-disabled'] = true;
  }

  if (props.tooltip) {
    accessibilityProps['aria-label'] = props.tooltip;
  }

  // Role for specific variants
  if (props.variant === 'link') {
    accessibilityProps.role = 'link';
  }

  return accessibilityProps;
};
